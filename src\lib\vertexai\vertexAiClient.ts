// src/lib/vertexai/vertexAiClient.ts
import { VertexAI } from '@google-cloud/vertexai';
import { logTokenUsage, createVertexAITokenTracking } from '@/lib/ai/tokenTracker';
import { VERTEX_AI_CONFIG } from '@/config/vertexai';
import { DocumentChunkingService } from '@/lib/services/DocumentChunkingService';
import { type Documento } from '@/types';
import * as Sentry from "@sentry/nextjs";

// Inicialización del cliente de Vertex AI
const vertexAI = new VertexAI({
  project: VERTEX_AI_CONFIG.projectId!,
  location: VERTEX_AI_CONFIG.location,
});

/**
 * Función central para hacer llamadas a los modelos Gemini en Vertex AI.
 */
export async function llamarGemini(
  prompt: string,
  options: {
    model: string;
    temperature?: number;
    maxOutputTokens?: number;
    activityName?: string;
    userId?: string;
  }
): Promise<string> {
  const { model, temperature, maxOutputTokens, activityName = 'VertexAI Call', userId } = options;

  try {
    const generativeModel = vertexAI.getGenerativeModel({
      model: model,
      generationConfig: {
        maxOutputTokens,
        temperature,
      },
    });

    const request = {
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
    };

    const result = await generativeModel.generateContent(request);
    const response = result.response;
    
    if (!response || !response.candidates || response.candidates.length === 0) {
      throw new Error('Vertex AI no devolvió una respuesta válida.');
    }

    // Tracking de tokens para Gemini
    if (response.usageMetadata) {
      const trackingData = createVertexAITokenTracking(
        activityName,
        model,
        {
          promptTokenCount: response.usageMetadata.promptTokenCount || 0,
          candidatesTokenCount: response.usageMetadata.candidatesTokenCount || 0,
          totalTokenCount: response.usageMetadata.totalTokenCount || 0
        },
        userId
      );
      await logTokenUsage(trackingData);
    }

    return response.candidates[0].content.parts[0].text || '';

  } catch (error: any) {
    Sentry.captureException(error, {
      tags: { section: "vertexai-client" },
      extra: {
        context: "Error calling Vertex AI API.",
        model,
        errorCode: error.code,
        errorDetails: error.details,
      },
    });

    console.error(`💥 [VERTEX_AI_CLIENT] Error al llamar a Gemini:`, error);
    throw new Error(`Error de Vertex AI: ${error.message || 'Error desconocido'}`);
  }
}

/**
 * Función de compatibilidad para reemplazar prepararDocumentos del openaiClient eliminado
 * Procesa documentos usando DocumentChunkingService
 */
export function prepararDocumentos(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  forceChunking: boolean = false,
  contentType: string = 'default'
): { content: string | any[]; wasChunked: boolean } {
  try {
    // Si solo hay un documento, usar DocumentChunkingService
    if (documentos.length === 1) {
      const documento = documentos[0] as Documento;

      const result = DocumentChunkingService.processDocument(documento, {
        enableChunking: true,
        forceChunking,
        contentType: contentType as any
      });

      if (result.wasChunked && Array.isArray(result.content)) {
        return {
          content: result.content,
          wasChunked: true
        };
      } else {
        return {
          content: documento.contenido || '',
          wasChunked: false
        };
      }
    } else {
      // Para múltiples documentos, combinar contenido
      const contenidoCombinado = documentos
        .map(doc => doc.contenido || '')
        .join('\n\n---\n\n');

      return {
        content: contenidoCombinado,
        wasChunked: false
      };
    }
  } catch (error) {
    console.error('Error en prepararDocumentos:', error);
    // Fallback: devolver contenido combinado sin chunking
    const contenidoCombinado = documentos
      .map(doc => doc.contenido || '')
      .join('\n\n---\n\n');

    return {
      content: contenidoCombinado,
      wasChunked: false
    };
  }
}
