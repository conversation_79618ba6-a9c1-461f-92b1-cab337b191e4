// src/features/chat/services/chatService.ts

import {
  iniciarChatConContexto,
  enviarMensajeEnChat
} from '@/lib/vertexai/vertexAiClient';
import {
  obtenerConversacionPorId,
  actualizarMetadataDeChat
} from '@/lib/supabase/conversacionesService';
import { PROMPT_PREGUNTAS } from '@/config/prompts';

// TTL del caché en horas (ej. 24 horas)
const CACHE_TTL_HOURS = 24;

/**
 * Gestiona un turno completo en una conversación con estado.
 * Orquesta la creación o continuación de un chat en caché.
 */
export const chatService = {
  async gestionarTurnoDeChat(
    conversacionId: string,
    documentoContenido: string,
    nuevoMensaje: string
  ): Promise<string> {
    const conversacion = await obtenerConversacionPorId(conversacionId);

    if (!conversacion) {
      throw new Error('La conversación no fue encontrada.');
    }

    const ahora = new Date();
    const expiracionCache = conversacion.cache_expires_at ? new Date(conversacion.cache_expires_at) : null;

    // Si no hay estado de proveedor o el caché ha expirado, iniciamos un nuevo chat.
    if (!conversacion.provider_conversation_state || !expiracionCache || ahora > expiracionCache) {
      console.log('🧠 [ChatService] No hay caché válido. Creando nueva sesión de chat...');

      const promptInicial = PROMPT_PREGUNTAS
        .replace('{documentos}', documentoContenido)
        .replace('{pregunta}', nuevoMensaje);

      const chatSession = await iniciarChatConContexto(promptInicial);
      const historialSerializado = chatSession.history; // El historial ya es JSON

      const nuevaExpiracion = new Date(ahora.getTime() + CACHE_TTL_HOURS * 60 * 60 * 1000);

      await actualizarMetadataDeChat(conversacionId, historialSerializado, nuevaExpiracion.toISOString());

      const ultimaRespuesta = chatSession.history[chatSession.history.length - 1];
      return ultimaRespuesta.parts[0].text || "No se pudo obtener la respuesta inicial.";

    } else {
      console.log(`🧠 [ChatService] Usando caché existente para la conversación ${conversacionId}.`);
      
      const historial = conversacion.provider_conversation_state as any[];
      
      const { respuesta, nuevoHistorial } = await enviarMensajeEnChat(historial, nuevoMensaje);

      const nuevaExpiracion = new Date(ahora.getTime() + CACHE_TTL_HOURS * 60 * 60 * 1000);
      await actualizarMetadataDeChat(conversacionId, nuevoHistorial, nuevaExpiracion.toISOString());

      return respuesta;
    }
  }
};
