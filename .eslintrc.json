{"extends": ["next/core-web-vitals"], "rules": {"@next/next/no-html-link-for-pages": "off", "react/no-unescaped-entities": "off", "@next/next/no-page-custom-font": "off", "no-restricted-imports": ["error", {"patterns": [{"group": ["@/lib/gemini.ts"], "message": "Use @/lib/gemini/index or specific service imports instead"}, {"group": ["@/lib/supabase.ts"], "message": "Use @/lib/supabase/index or specific service imports instead"}, {"group": ["@/features/auth/services/authService"], "message": "Use @/lib/supabase/authService instead"}, {"group": ["@/features/conversations/services/conversacionesService"], "message": "Use @/lib/supabase/conversacionesService instead"}, {"group": ["@/features/dashboard/services/dashboardService"], "message": "Use @/lib/supabase/dashboardService instead"}, {"group": ["@/features/documents/services/documentosService"], "message": "Use @/lib/supabase/documentosService instead"}, {"group": ["@/features/flashcards/services/flashcardsService"], "message": "Use @/lib/supabase/flashcardsService instead"}, {"group": ["@/features/tests/services/testsService"], "message": "Use @/lib/supabase/testsService instead"}, {"group": ["@/features/flashcards/services/flashcardGenerator"], "message": "Use @/lib/gemini/flashcardGenerator instead"}, {"group": ["@/features/tests/services/testGenerator"], "message": "Use @/lib/gemini/testGenerator instead"}]}]}}